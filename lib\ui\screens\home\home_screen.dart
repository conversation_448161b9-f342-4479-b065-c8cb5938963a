import 'dart:async';
import 'dart:developer';
import 'dart:io';
import 'package:app_tracking_transparency/app_tracking_transparency.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/ads/interstitial_ad_cubit.dart';
import 'package:flutterquiz/features/ads/rewarded_ad_cubit.dart';
import 'package:flutterquiz/features/auth/authRepository.dart';
import 'package:flutterquiz/features/auth/cubits/referAndEarnCubit.dart';
import 'package:flutterquiz/features/badges/cubits/badgesCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/battleRoomCubit.dart';
import 'package:flutterquiz/features/battleRoom/cubits/multiUserBattleRoomCubit.dart';
import 'package:flutterquiz/features/exam/cubits/examCubit.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardAllTimeCubit.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardDailyCubit.dart';
import 'package:flutterquiz/features/leaderBoard/cubit/leaderBoardMonthlyCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/deleteAccountCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/updateUserDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/uploadProfileCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementLocalDataSource.dart';
import 'package:flutterquiz/features/profileManagement/profileManagementRepository.dart';
import 'package:flutterquiz/features/quiz/cubits/contestCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/cubits/quizzone_category_cubit.dart';
import 'package:flutterquiz/features/quiz/cubits/subCategoryCubit.dart';
import 'package:flutterquiz/features/quiz/models/contest.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/battle/create_or_join_screen.dart';
import 'package:flutterquiz/ui/screens/home/<USER>';
import 'package:flutterquiz/ui/screens/home/<USER>/app_under_maintenance_dialog.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/supercategory_quiz_zone.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/update_app_container.dart';
import 'package:flutterquiz/ui/screens/home/<USER>/user_achievements.dart';
import 'package:flutterquiz/ui/screens/menu/menu_screen.dart';
import 'package:flutterquiz/ui/screens/quiz/categoryScreen.dart';
import 'package:flutterquiz/ui/screens/subscription/SubscriptionService.dart';
import 'package:flutterquiz/ui/screens/subscription/paywall.dart';
import 'package:flutterquiz/ui/widgets/all.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/reminder_manager.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:http/http.dart' as http;
import 'package:iconsax/iconsax.dart';
import 'package:iconsax_plus/iconsax_plus.dart';
import 'package:path_provider/path_provider.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:salomon_bottom_bar/salomon_bottom_bar.dart';
import 'package:url_launcher/url_launcher_string.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({required this.isGuest, super.key});

  final bool isGuest;

  @override
  State<HomeScreen> createState() => _HomeScreenState();

  static Route<HomeScreen> route(RouteSettings routeSettings) {
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<ReferAndEarnCubit>(
            create: (_) => ReferAndEarnCubit(AuthRepository()),
          ),
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
          BlocProvider<UpdateUserDetailCubit>(
            create: (_) => UpdateUserDetailCubit(ProfileManagementRepository()),
          ),
        ],
        child: HomeScreen(isGuest: routeSettings.arguments! as bool),
      ),
    );
  }
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  /// Quiz Zone globals
  int oldCategoriesToShowCount = 0;
  bool isCateListExpanded = false;
  bool canExpandCategoryList = false;

  final flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

  ReminderManager reminderManager = ReminderManager();

  void checkReminder() async {
    //   final pres = await SharedPreferences.getInstance();
    // await pres.clear();
    await reminderManager.handleReminderSetup(context);
  }

  Future<void> _checkStoreKitConfig() async {
    try {
      final offerings = await Purchases.getOfferings();
      print('''
🔍 فحص StoreKit:
- هل توجد عروض: ${offerings.all.isNotEmpty}
- عدد العروض: ${offerings.all.length}
- العرض الحالي: ${offerings.current?.identifier}
- المنتجات المتاحة: ${offerings.current?.availablePackages.map((p) => p.storeProduct.identifier).join(', ')}
''');
    } catch (e) {
      print('❌ خطأ في فحص StoreKit: $e');
    }
  }

  List<String> battleName = ['groupPlay', 'battleQuiz'];

  List<String> battleImg = [Assets.groupBattleIcon, Assets.oneVsOneIcon];

  List<String> examSelf = ['exam', 'selfChallenge'];

  List<String> examSelfDesc = ['desExam', 'challengeYourselfLbl'];

  List<String> examSelfImg = [Assets.examQuizIcon, Assets.selfChallengeIcon];

  List<String> battleDesc = ['desGroupPlay', 'desBattleQuiz'];

  List<String> playDifferentZone = [
    //'dailyQuiz',
    'funAndLearn',
    // 'guessTheWord',
    'audioQuestions',
    // 'mathMania',
    // 'truefalse',
  ];

  List<String> playDifferentImg = [
    // Assets.dailyQuizIcon,
    Assets.funNLearnIcon,
    // Assets.guessTheWordIcon,
    Assets.audioQuizIcon,
    // Assets.mathsQuizIcon,
    // Assets.trueFalseQuizIcon,
  ];

  List<String> playDifferentZoneDesc = [
    'desDailyQuiz',
    'desFunAndLearn',
    // 'desGuessTheWord',
    // 'desAudioQuestions',
    // 'desMathMania',
    // 'desTrueFalse',
  ];

  // Screen dimensions
  double get scrWidth => MediaQuery.of(context).size.width;

  double get scrHeight => MediaQuery.of(context).size.height;

  // HomeScreen horizontal margin, change from here
  double get hzMargin => scrWidth * UiUtils.hzMarginPct;

  double get _statusBarPadding => MediaQuery.of(context).padding.top;

  // TextStyles
  // check build() method
  late var _boldTextStyle = TextStyle(
    fontWeight: FontWeights.bold,
    fontSize: 18,
    color: Theme.of(context).colorScheme.onTertiary,
  );

  ///
  late String _currLangId;
  late final SystemConfigCubit _sysConfigCubit;
  final _quizZoneId =
      UiUtils.getCategoryTypeNumberFromQuizType(QuizTypes.quizZone);

  @override
  void didChangeDependencies() {
    if (_navBodies.isEmpty) {
      _navBodies
        ..add(
          buildHomeBody(),
        )
        // ..add(
        //    const PdfsScreen(
        //      fromNav: true,
        //    ),
        // )
        ..add(
          MultiBlocProvider(
            providers: [
              BlocProvider<LeaderBoardMonthlyCubit>(
                create: (_) => LeaderBoardMonthlyCubit(),
              ),
              BlocProvider<LeaderBoardDailyCubit>(
                create: (_) => LeaderBoardDailyCubit(),
              ),
              BlocProvider<LeaderBoardAllTimeCubit>(
                create: (_) => LeaderBoardAllTimeCubit(),
              ),
            ],
            child: const LeaderBoardScreen(
              fromNav: true,
            ),
          ),
        )
        ..add(
          MultiBlocProvider(
            providers: [
              BlocProvider<DeleteAccountCubit>(
                create: (_) =>
                    DeleteAccountCubit(ProfileManagementRepository()),
              ),
              BlocProvider<UploadProfileCubit>(
                create: (_) =>
                    UploadProfileCubit(ProfileManagementRepository()),
              ),
              BlocProvider<UpdateUserDetailCubit>(
                create: (_) =>
                    UpdateUserDetailCubit(ProfileManagementRepository()),
              ),
            ],
            child: MenuScreen(isGuest: widget.isGuest, fromNav: true),
          ),
        );

      WidgetsBinding.instance.addPostFrameCallback(
        (_) {
          if (context.read<SystemConfigCubit>().isQuizZoneEnabled) {
            _navBodies.insert(
              1,
              const CategoryScreen(
                quizType: QuizTypes.quizZone,
                fromNav: true,
              ),
            );
          }
        },
      );
    }
    super.didChangeDependencies();
  }

  // bool _isPremium = false;
  @override
  void initState() {
    super.initState();
    checkReminder();
    _checkStoreKitConfig();
    Future.delayed(
        const Duration(milliseconds: 500), _requestTrackingPermission);

    setupInteractedMessage();
    _initLocalNotification();
    setQuizMenu();
    checkForUpdates();
    showAppUnderMaintenanceDialog();
    Future.delayed(Duration.zero, () async {
      // ignore: use_build_context_synchronously
      await context.read<RewardedAdCubit>().createDailyRewardAd(context);
      // ignore: use_build_context_synchronously
      context.read<InterstitialAdCubit>().createInterstitialAd(context);
      (context);
      // تحديث حالة الاشتراك
      // ignore: use_build_context_synchronously
      context.read<SubscriptionCubit>().checkSubscription();
    });

    WidgetsBinding.instance.addObserver(this);

    ///
    _currLangId = UiUtils.getCurrentQuizLanguageId(context);
    _sysConfigCubit = context.read<SystemConfigCubit>();
    final quizCubit = context.read<QuizCategoryCubit>();
    final quizZoneCubit = context.read<QuizoneCategoryCubit>();

    if (widget.isGuest) {
      quizCubit.getQuizCategory(languageId: _currLangId, type: _quizZoneId);
      quizZoneCubit.getQuizCategory(languageId: _currLangId);
    } else {
      fetchUserDetails();

      quizCubit.getQuizCategoryWithUserId(
        languageId: _currLangId,
        type: _quizZoneId,
      );
      quizZoneCubit.getQuizCategoryWithUserId(languageId: _currLangId);
      context.read<ContestCubit>().getContest(languageId: _currLangId);
    }

    // أضف هذا الكود في بداية initState
    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   _checkFirstTimeAndShowInterests();
    // });
  }

  Future<void> _requestTrackingPermission() async {
    // التأكد من أن الإذن يُطلب قبل أي عملية جمع بيانات
    WidgetsBinding.instance.addPostFrameCallback((_) async {
      final status = await AppTrackingTransparency.trackingAuthorizationStatus;
      if (status == TrackingStatus.notDetermined) {
        // تأخير بسيط للتأكد من ظهور الطلب بعد تحميل التطبيق
        await Future.delayed(const Duration(seconds: 1));
        await AppTrackingTransparency.requestTrackingAuthorization();
      }
      final uuid = await AppTrackingTransparency.getAdvertisingIdentifier();
      debugPrint('IDFA: $uuid');
    });
  }

  void showAppUnderMaintenanceDialog() {
    Future.delayed(Duration.zero, () {
      if (_sysConfigCubit.isAppUnderMaintenance) {
        showDialog<void>(
          // ignore: use_build_context_synchronously
          context: context,
          builder: (_) => const AppUnderMaintenanceDialog(),
        );
      }
    });
  }

  Future<void> _initLocalNotification() async {
    const initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');
    final initializationSettingsIOS = const DarwinInitializationSettings();

    final initializationSettings = InitializationSettings(
      android: initializationSettingsAndroid,
      iOS: initializationSettingsIOS,
    );
    await flutterLocalNotificationsPlugin.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onTapLocalNotification,
    );

    /// Request Permissions for IOS
    if (Platform.isIOS) {
      await flutterLocalNotificationsPlugin
          .resolvePlatformSpecificImplementation<
              IOSFlutterLocalNotificationsPlugin>()
          ?.requestPermissions();
    }
  }

  void setQuizMenu() {
    Future.delayed(Duration.zero, () {
      if (!_sysConfigCubit.isDailyQuizEnabled) {
        playDifferentZone.removeWhere((e) => e == 'dailyQuiz');
        playDifferentImg.removeWhere((e) => e == Assets.dailyQuizIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desDailyQuiz');
      }

      if (!_sysConfigCubit.isTrueFalseQuizEnabled) {
        playDifferentZone.removeWhere((e) => e == 'truefalse');
        playDifferentImg.removeWhere((e) => e == Assets.trueFalseQuizIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desTrueFalse');
      }

      if (!_sysConfigCubit.isFunNLearnEnabled) {
        playDifferentZone.removeWhere((e) => e == 'funAndLearn');
        playDifferentImg.removeWhere((e) => e == Assets.funNLearnIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desFunAndLearn');
      }

      if (!_sysConfigCubit.isGuessTheWordEnabled) {
        playDifferentZone.removeWhere((e) => e == 'guessTheWord');
        playDifferentImg.removeWhere((e) => e == Assets.guessTheWordIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desGuessTheWord');
      }

      if (!_sysConfigCubit.isAudioQuizEnabled) {
        playDifferentZone.removeWhere((e) => e == 'audioQuestions');
        playDifferentImg.removeWhere((e) => e == Assets.guessTheWordIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desAudioQuestions');
      }

      if (!_sysConfigCubit.isMathQuizEnabled) {
        playDifferentZone.removeWhere((e) => e == 'mathMania');
        playDifferentImg.removeWhere((e) => e == Assets.mathsQuizIcon);
        playDifferentZoneDesc.removeWhere((e) => e == 'desMathMania');
      }

      if (!_sysConfigCubit.isExamQuizEnabled) {
        examSelf.removeWhere((e) => e == 'exam');
        examSelfDesc.removeWhere((e) => e == 'desExam');
        examSelfImg.removeWhere((e) => e == Assets.examQuizIcon);
      }

      if (!_sysConfigCubit.isSelfChallengeQuizEnabled) {
        examSelf.removeWhere((e) => e == 'selfChallenge');
        examSelfDesc.removeWhere((e) => e == 'challengeYourselfLbl');
        examSelfImg.removeWhere((e) => e == Assets.selfChallengeIcon);
      }

      if (!_sysConfigCubit.isGroupBattleEnabled) {
        battleName.removeWhere((e) => e == 'groupPlay');
        battleImg.removeWhere((e) => e == Assets.groupBattleIcon);
        battleDesc.removeWhere((e) => e == 'desGroupPlay');
      }

      if (!_sysConfigCubit.isOneVsOneBattleEnabled &&
          !_sysConfigCubit.isRandomBattleEnabled) {
        battleName.removeWhere((e) => e == 'battleQuiz');
        battleImg.removeWhere((e) => e == Assets.groupBattleIcon);
        battleDesc.removeWhere((e) => e == 'desBattleQuiz');
      }
      setState(() {});
    });
  }

  late bool showUpdateContainer = false;

  Future<void> checkForUpdates() async {
    await Future<void>.delayed(Duration.zero);
    if (_sysConfigCubit.isForceUpdateEnable) {
      try {
        final forceUpdate =
            await UiUtils.forceUpdate(_sysConfigCubit.appVersion);

        if (forceUpdate) {
          setState(() => showUpdateContainer = true);
        }
      } catch (e) {
        log('Force Update Error', error: e);
      }
    }
  }

  Future<void> setupInteractedMessage() async {
    if (Platform.isIOS) {
      await FirebaseMessaging.instance.requestPermission(
        announcement: true,
        provisional: true,
      );
    } else {
      final isGranted = (await Permission.notification.status).isGranted;
      if (!isGranted) await Permission.notification.request();
    }
    await FirebaseMessaging.instance.getInitialMessage();
    FirebaseMessaging.onMessageOpenedApp.listen(_handleMessage);
    // handle background notification
    FirebaseMessaging.onBackgroundMessage(UiUtils.onBackgroundMessage);
    //handle foreground notification
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      log('Notification arrives : ${message.toMap()}');
      final data = message.data;
      log(data.toString(), name: 'notification data msg');
      final title = data['title'].toString();
      final body = data['body'].toString();
      final type = data['type'].toString();
      final image = data['image'].toString();

      //if notification type is badges then update badges in cubit list
      if (type == 'badges') {
        Future.delayed(Duration.zero, () {
          context.read<BadgesCubit>().unlockBadge(data['badge_type'] as String);
        });
      }

      if (type == 'payment_request') {
        Future.delayed(Duration.zero, () {
          context.read<UserDetailsCubit>().updateCoins(
                addCoin: true,
                coins: int.parse(data['coins'] as String),
              );
        });
      }
      log(image, name: 'notification image data');
      //payload is some data you want to pass in local notification
      if (image != 'null' && image.isNotEmpty) {
        log('image ${image.runtimeType}');
        generateImageNotification(title, body, image, type, type);
      } else {
        generateSimpleNotification(title, body, type);
      }
    });
  }

  //quiz_type according to the notification category
  QuizTypes _getQuizTypeFromCategory(String category) {
    return switch (category) {
      'audio-question-category' => QuizTypes.audioQuestions,
      'guess-the-word-category' => QuizTypes.guessTheWord,
      'fun-n-learn-category' => QuizTypes.funAndLearn,
      _ => QuizTypes.quizZone,
    };
  }

  // notification type is category then move to category screen
  Future<void> _handleMessage(RemoteMessage message) async {
    try {
      if (message.data['type'].toString().contains('category')) {
        await Navigator.of(context).pushNamed(
          Routes.category,
          arguments: {
            'quizType':
                _getQuizTypeFromCategory(message.data['type'] as String),
          },
        );
      } else if (message.data['type'] == 'badges') {
        //if user open app by tapping
        UiUtils.updateBadgesLocally(context);
        await Navigator.of(context).pushNamed(Routes.badges);
      } else if (message.data['type'] == 'payment_request') {
        await Navigator.of(context).pushNamed(Routes.wallet);
      }
    } catch (e) {
      log(e.toString(), error: e);
    }
  }

  Future<void> _onTapLocalNotification(NotificationResponse? payload) async {
    final type = payload!.payload ?? '';
    if (type == 'badges') {
      await Navigator.of(context).pushNamed(Routes.badges);
    } else if (type.contains('category')) {
      await Navigator.of(context).pushNamed(
        Routes.category,
        arguments: {
          'quizType': _getQuizTypeFromCategory(type),
        },
      );
    } else if (type == 'payment_request') {
      await Navigator.of(context).pushNamed(Routes.wallet);
    }
  }

  Future<void> generateImageNotification(
    String title,
    String msg,
    String image,
    String payloads,
    String type,
  ) async {
    final largeIconPath = await _downloadAndSaveFile(image, 'largeIcon');
    final bigPicturePath = await _downloadAndSaveFile(image, 'bigPicture');
    final bigPictureStyleInformation = BigPictureStyleInformation(
      FilePathAndroidBitmap(bigPicturePath),
      hideExpandedLargeIcon: true,
      contentTitle: title,
      htmlFormatContentTitle: true,
      summaryText: msg,
      htmlFormatSummaryText: true,
    );
    final androidPlatformChannelSpecifics = AndroidNotificationDetails(
      packageName,
      appName,
      icon: '@drawable/ic_notification',
      channelDescription: appName,
      largeIcon: FilePathAndroidBitmap(largeIconPath),
      styleInformation: bigPictureStyleInformation,
    );
    final platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
    );
    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      msg,
      platformChannelSpecifics,
      payload: payloads,
    );
  }

  Future<String> _downloadAndSaveFile(String url, String fileName) async {
    final directory = await getApplicationDocumentsDirectory();
    final filePath = '${directory.path}/$fileName';
    final response = await http.get(Uri.parse(url));
    final file = File(filePath);
    await file.writeAsBytes(response.bodyBytes);

    return filePath;
  }

  // notification on foreground
  Future<void> generateSimpleNotification(
    String title,
    String body,
    String payloads,
  ) async {
    const androidPlatformChannelSpecifics = AndroidNotificationDetails(
      packageName, //channel id
      appName, //channel name
      channelDescription: appName,
      importance: Importance.max,
      priority: Priority.high,
      ticker: 'ticker',
      icon: '@drawable/ic_notification',
    );

    const platformChannelSpecifics = NotificationDetails(
      android: androidPlatformChannelSpecifics,
      iOS: DarwinNotificationDetails(),
    );

    await flutterLocalNotificationsPlugin.show(
      0,
      title,
      body,
      platformChannelSpecifics,
      payload: payloads,
    );
  }

  @override
  void dispose() {
    ProfileManagementLocalDataSource.updateReversedCoins(0);
    WidgetsBinding.instance.removeObserver(this);
    // إزالة المستمع عند إغلاق الشاشة
    Purchases.removeCustomerInfoUpdateListener((_) {});
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    //show you left the game
    if (state == AppLifecycleState.resumed) {
      UiUtils.needToUpdateCoinsLocally(context);
    } else {
      ProfileManagementLocalDataSource.updateReversedCoins(0);
    }
  }

  // void _onTapProfile() => Navigator.of(context).pushNamed(
  //       Routes.menuScreen,
  //       arguments: widget.isGuest,
  //     );

  // void _onTapLeaderboard() => Navigator.of(context).pushNamed(
  //       widget.isGuest ? Routes.login : Routes.leaderBoard,
  //     );

  // void _onPressedZone(String index) {
  //   if (widget.isGuest) {
  //     _showLoginDialog();
  //     return;
  //   }

  //   switch (index) {
  //     case 'dailyQuiz':
  //       Navigator.of(context).pushNamed(
  //         Routes.quiz,
  //         arguments: {
  //           'quizType': QuizTypes.dailyQuiz,
  //           'numberOfPlayer': 1,
  //           'quizName': 'Daily Quiz',
  //         },
  //       );
  //       return;
  //     case 'funAndLearn':
  //       Navigator.of(context).pushNamed(
  //         Routes.category,
  //         arguments: {
  //           'quizType': QuizTypes.funAndLearn,
  //         },
  //       );
  //       return;
  //     case 'guessTheWord':
  //       Navigator.of(context).pushNamed(
  //         Routes.category,
  //         arguments: {
  //           'quizType': QuizTypes.guessTheWord,
  //         },
  //       );
  //       return;
  //     case 'audioQuestions':
  //       Navigator.of(context).pushNamed(
  //         Routes.category,
  //         arguments: {
  //           'quizType': QuizTypes.audioQuestions,
  //         },
  //       );
  //       return;
  //     case 'mathMania':
  //       Navigator.of(context).pushNamed(
  //         Routes.category,
  //         arguments: {
  //           'quizType': QuizTypes.mathMania,
  //         },
  //       );
  //       return;
  //     case 'truefalse':
  //       Navigator.of(context).pushNamed(
  //         Routes.quiz,
  //         arguments: {
  //           'quizType': QuizTypes.trueAndFalse,
  //           'numberOfPlayer': 1,
  //           'quizName': 'True/False Quiz',
  //         },
  //       );
  //       return;
  //   }
  // }

  void _onPressedSelfExam(String index) {
    if (widget.isGuest) {
      _showLoginDialog();
      return;
    }

    if (index == 'exam') {
      context.read<ExamCubit>().updateState(ExamInitial());
      Navigator.of(context).pushNamed(Routes.exams);
    } else if (index == 'selfChallenge') {
      context.read<QuizCategoryCubit>().updateState(QuizCategoryInitial());
      context.read<SubCategoryCubit>().updateState(SubCategoryInitial());
      Navigator.of(context).pushNamed(Routes.selfChallenge);
    }
  }

  void _onPressedBattle(String index) {
    if (widget.isGuest) {
      _showLoginDialog();
      return;
    }

    context.read<QuizCategoryCubit>().updateState(QuizCategoryInitial());
    if (index == 'groupPlay') {
      context
          .read<MultiUserBattleRoomCubit>()
          .updateState(MultiUserBattleRoomInitial());

      Navigator.of(context).push(
        CupertinoPageRoute<void>(
          builder: (_) => BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (context) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
            child: CreateOrJoinRoomScreen(
              quizType: QuizTypes.groupPlay,
              title: context.tr('groupPlay')!,
            ),
          ),
        ),
      );
    } else if (index == 'battleQuiz') {
      context.read<BattleRoomCubit>().updateState(
            BattleRoomInitial(),
            cancelSubscription: true,
          );

      if (_sysConfigCubit.isRandomBattleEnabled) {
        // TODO(J): if only random battle is enabled then in home screen also it should show random battle ? maybe
        Navigator.of(context).pushNamed(Routes.randomBattle);
      } else {
        Navigator.of(context).push(
          CupertinoPageRoute<CreateOrJoinRoomScreen>(
            builder: (_) => BlocProvider<UpdateScoreAndCoinsCubit>(
              create: (_) =>
                  UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
              child: CreateOrJoinRoomScreen(
                quizType: QuizTypes.oneVsOneBattle,
                title: context.tr('playWithFrdLbl')!,
              ),
            ),
          ),
        );
      }
    }
  }

  Future<void> _showLoginDialog() {
    return showLoginDialog(
      context,
      onTapYes: () {
        Navigator.of(context).pop();
        Navigator.of(context).pushNamed(Routes.login);
      },
    );
  }

  late String _userName = context.tr('guest')!;
  late String _userProfileImg = Assets.profile('3.png');

  Widget _buildProfileContainer() {
    return BlocBuilder<UserDetailsCubit, UserDetailsState>(
      builder: (context, state) {
        final size = MediaQuery.of(context).size;
        return Container(
          margin: EdgeInsets.only(
            top: _statusBarPadding * .2,
            left: hzMargin,
            right: hzMargin,
          ),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  Theme.of(context).primaryColor.withOpacity(0.1),
                  Theme.of(context).primaryColor.withOpacity(0.05),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                // Profile Image
                Container(
                  padding: const EdgeInsets.all(3),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        Theme.of(context).primaryColor,
                        Theme.of(context).primaryColor.withOpacity(0.7),
                      ],
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).primaryColor.withOpacity(0.3),
                        blurRadius: 12,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Theme.of(context).scaffoldBackgroundColor,
                    ),
                    child: ClipOval(
                      child: SizedBox(
                        width: size.width * 0.13,
                        height: size.width * 0.13,
                        child: QImage.circular(imageUrl: _userProfileImg),
                      ),
                    ),
                  ),
                ),

                SizedBox(width: size.width * .03),

                // User Info
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        '${context.tr(helloKey)!} ${widget.isGuest ? context.tr('guest')! : _userName}',
                        maxLines: 1,
                        style: _boldTextStyle.copyWith(
                          fontSize: 18,
                          color: Theme.of(context).colorScheme.onTertiary,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      // const SizedBox(height: 4),
                      // Text(
                      //   context.tr(letsPlay)!,
                      //   style: TextStyle(
                      //     fontSize: 14,
                      //     color: Theme.of(context)
                      //         .colorScheme
                      //         .onTertiary
                      //         .withOpacity(0.7),
                      //   ),
                      //   maxLines: 1,
                      //   overflow: TextOverflow.ellipsis,
                      // ),
                    ],
                  ),
                ),

                // Premium Button
                // Premium Button
                BlocBuilder<SubscriptionCubit, SubscriptionState>(
                  builder: (context, subscriptionState) {
                    return Container(
                      width: size.width * 0.11,
                      height: size.width * 0.11,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(15),
                        gradient: LinearGradient(
                          colors: subscriptionState.isSubscribed
                              ? [Colors.purple.shade400, Colors.purple.shade700]
                              : [Colors.amber.shade300, Colors.amber.shade600],
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: (subscriptionState.isSubscribed
                                    ? Colors.purple
                                    : Colors.amber)
                                .withOpacity(0.3),
                            blurRadius: 8,
                            offset: const Offset(0, 3),
                          ),
                        ],
                      ),
                      child: Material(
                        color: Colors.transparent,
                        child: InkWell(
                          borderRadius: BorderRadius.circular(15),
                          onTap: subscriptionState.isSubscribed
                              ? null
                              : () async {
                                  try {
                                    final offerings =
                                        await Purchases.getOfferings();
                                    if (!mounted) return;

                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (_) => Paywall(
                                          offering: offerings.current,
                                          onError: (String message) {
                                            return Center(
                                              child: Column(
                                                mainAxisAlignment:
                                                    MainAxisAlignment.center,
                                                children: [
                                                  Icon(
                                                    Icons.error_outline,
                                                    size: 64,
                                                    color: Colors.red[300],
                                                  ),
                                                  const SizedBox(height: 16),
                                                  Text(
                                                    message,
                                                    textAlign: TextAlign.center,
                                                    style: const TextStyle(
                                                      fontSize: 16,
                                                      color: Colors.white,
                                                    ),
                                                  ),
                                                  const SizedBox(height: 24),
                                                  ElevatedButton(
                                                    onPressed: () =>
                                                        Navigator.pop(context),
                                                    child: const Text('العودة'),
                                                  ),
                                                ],
                                              ),
                                            );
                                          },
                                        ),
                                      ),
                                    );
                                  } catch (e) {
                                    if (!mounted) return;
                                    Navigator.of(context).push(
                                      MaterialPageRoute(
                                        builder: (_) => const Paywall(
                                          offering: null,
                                        ),
                                      ),
                                    );
                                  }
                                },
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                subscriptionState.isSubscribed
                                    ? Icons.verified
                                    : Icons.workspace_premium,
                                color: Colors.white,
                                size: size.width * 0.045,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                subscriptionState.isSubscribed
                                    ? 'مميز'
                                    : 'ترقية',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: size.width * 0.022,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    );
                  },
                ),
                SizedBox(width: size.width * 0.02),

                // // Interests Selection Button
                // SizedBox(width: size.width * 0.02),
                // Container(
                //   width: size.width * 0.11,
                //   height: size.width * 0.11,
                //   decoration: BoxDecoration(
                //     borderRadius: BorderRadius.circular(15),
                //     gradient: LinearGradient(
                //       colors: [
                //         Colors.teal.shade400,
                //         Colors.teal.shade700,
                //       ],
                //       begin: Alignment.topLeft,
                //       end: Alignment.bottomRight,
                //     ),
                //     boxShadow: [
                //       BoxShadow(
                //         color: Colors.teal.withOpacity(0.3),
                //         blurRadius: 8,
                //         offset: const Offset(0, 3),
                //       ),
                //     ],
                //   ),
                //   child: Material(
                //     color: Colors.transparent,
                //     child: InkWell(
                //       borderRadius: BorderRadius.circular(15),
                //       onTap: () => showQuizLanguageSelectorSheet(context),
                //       child: Column(
                //         mainAxisAlignment: MainAxisAlignment.center,
                //         children: [
                //           Icon(
                //             Icons.interests_rounded,
                //             color: Colors.white,
                //             size: size.width * 0.045,
                //           ),
                //           const SizedBox(height: 2),
                //           Text(
                //             'الاهتمام',
                //             style: TextStyle(
                //               color: Colors.white,
                //               fontSize: size.width * 0.022,
                //               fontWeight: FontWeight.bold,
                //             ),
                //           ),
                //         ],
                //       ),
                //     ),
                //   ),
                // ),
                // SizedBox(width: size.width * 0.02),

                // Settings Button
                Container(
                  width: size.width * 0.11,
                  height: size.width * 0.11,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(15),
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                  ),
                  child: IconButton(
                    onPressed: () =>
                        Navigator.of(context).pushNamed(Routes.settings),
                    icon: Icon(
                      Icons.settings,
                      color: Theme.of(context).colorScheme.onTertiary,
                      size: size.width * 0.06,
                    ),
                  ),
//                   ElevatedButton(
//   onPressed: () async {
//     final ratingService = RatingService();
//     await ratingService.showRatingPrompt();
//   },
//   child: Text('اختبار نافذة التقييم'),
// ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
  // Icon(
  //             Iconsax.medal_star,
  //             color: Theme.of(context).primaryColor,
  //             size: 22,
  //           ),
  //           const SizedBox(width: 8),
  //             Text(
  //               context.tr(contest) ?? contest,
  //               style: _boldTextStyle,
  //             ),

  Widget _buildCategory() {
    return Column(
      children: [
        Padding(
          padding: EdgeInsets.symmetric(horizontal: hzMargin),
          child: Row(
            children: [
              Icon(
                Iconsax.category,
                color: Theme.of(context).primaryColor,
                size: 22,
              ),
              const SizedBox(width: 8),
              Text(
                "الدورات والتدريبات",
                style: _boldTextStyle,
              ),
              const Spacer(),
              GestureDetector(
                onTap: () {
                  widget.isGuest
                      ? _showLoginDialog()
                      : Navigator.of(context).pushNamed(
                          Routes.category,
                          arguments: {'quizType': QuizTypes.quizZone},
                        );
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                  decoration: BoxDecoration(
                    color: Theme.of(context).primaryColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    context.tr(viewAllKey) ?? viewAllKey,
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
        Container(
          margin: EdgeInsets.only(
            left: hzMargin,
            right: hzMargin,
            top: 10,
            bottom: 26,
          ),
          child: quizZoneCategories(),
        ),
      ],
    );
  }

  Widget quizZoneCategories() {
    return SuperCategoryQuizZone(isGuest: widget.isGuest);
  }

  Widget _buildLiveContestSection() {
    void onTapViewAll() {
      if (_sysConfigCubit.isContestEnabled) {
        Navigator.of(context).pushNamed(Routes.contest);
      } else {
        UiUtils.showSnackBar(
          context.tr(currentlyNotAvailableKey)!,
          context,
        );
      }
    }

    return Container(
      margin: EdgeInsets.symmetric(horizontal: hzMargin, vertical: 15),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.only(bottom: 15),
            child: Row(
              children: [
                Icon(
                  Iconsax.medal_star,
                  color: Theme.of(context).primaryColor,
                  size: 22,
                ),
                const SizedBox(width: 8),
                Text(
                  context.tr(contest) ?? contest,
                  style: _boldTextStyle,
                ),
                const Spacer(),
                GestureDetector(
                  onTap: onTapViewAll,
                  child: Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      context.tr(viewAllKey) ?? viewAllKey,
                      style: TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),

          // محتوى المسابقات
          BlocConsumer<ContestCubit, ContestState>(
            bloc: context.read<ContestCubit>(),
            listener: (context, state) {
              if (state is ContestFailure) {
                if (state.errorMessage == errorCodeUnauthorizedAccess) {
                  showAlreadyLoggedInDialog(context);
                }
              }
            },
            builder: (context, state) {
              if (state is ContestFailure) {
                return _buildErrorContainer(state.errorMessage);
              }

              if (state is ContestSuccess) {
                final live = state.contestList.live;

                if (live.contestDetails.isEmpty ||
                    live.errorMessage.isNotEmpty) {
                  return _buildEmptyContests();
                }

                return SizedBox(
                  height: 220,
                  child: ListView.builder(
                    physics: const BouncingScrollPhysics(),
                    scrollDirection: Axis.horizontal,
                    itemCount: live.contestDetails.length,
                    itemBuilder: (context, index) {
                      final contest = live.contestDetails[index];
                      return _buildContestCard(contest);
                    },
                  ),
                );
              }

              return const Center(
                child: CircularProgressContainer(),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildContestCard(ContestDetails contest) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;

    return GestureDetector(
      onTap: () {
        if (!widget.isGuest) {
          Navigator.of(context).pushNamed(
            Routes.quiz,
            arguments: {
              'numberOfPlayer': 1,
              'quizType': QuizTypes.contest,
              'contestId': contest.id,
            },
          );
        } else {
          _showLoginDialog();
        }
      },
      child: Container(
        width: isSmallScreen ? 260 : 280,
        margin: const EdgeInsets.only(right: 17),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.background,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // صورة المسابقة والعنوان
            Stack(
              children: [
                Container(
                  height: isSmallScreen ? 120 : 140,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                    image: DecorationImage(
                      image: NetworkImage(
                        contest.image ?? 'https://placeholder.com/300x200',
                      ),
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
                Container(
                  height: isSmallScreen ? 120 : 140,
                  decoration: BoxDecoration(
                    borderRadius: const BorderRadius.vertical(
                      top: Radius.circular(20),
                    ),
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.7),
                      ],
                    ),
                  ),
                ),
                Positioned(
                  bottom: 12,
                  right: 12,
                  left: 12,
                  child: Text(
                    contest.name ?? '',
                    textAlign: TextAlign.right,
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: isSmallScreen ? 16 : 18,
                      fontWeight: FontWeight.bold,
                    ),
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),

            // تفاصيل المسابقة
            Expanded(
              child: Padding(
                padding: EdgeInsets.all(isSmallScreen ? 8 : 10),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    if (contest.description != null &&
                        contest.description!.isNotEmpty)
                      Expanded(
                        child: Text(
                          contest.description!,
                          style: TextStyle(
                            color: Theme.of(context)
                                .colorScheme
                                .onSurface
                                .withOpacity(0.8),
                            fontSize: isSmallScreen ? 12 : 13,
                            height: 1.3,
                          ),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                          textAlign: TextAlign.right,
                        ),
                      ),

                    // معلومات المشاركين والأزرار
                    Container(
                      margin: const EdgeInsets.only(top: 8),
                      child: Row(
                        children: [
                          // عدد المشاركين
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                Icons.group_outlined,
                                color: Theme.of(context).colorScheme.onSurface,
                                size: isSmallScreen ? 16 : 20,
                              ),
                              const SizedBox(width: 4),
                              Text(
                                "${contest.participants ?? '0'} مشارك",
                                style: TextStyle(
                                  color:
                                      Theme.of(context).colorScheme.onSurface,
                                  fontSize: isSmallScreen ? 12 : 13,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),

                          const Spacer(),

                          // زر المشاركة
                          Container(
                            padding: EdgeInsets.symmetric(
                              horizontal: isSmallScreen ? 12 : 16,
                              vertical: isSmallScreen ? 6 : 8,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  Theme.of(context).primaryColor,
                                  Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.8),
                                ],
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                              ),
                              borderRadius: BorderRadius.circular(20),
                              boxShadow: [
                                BoxShadow(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 3),
                                ),
                              ],
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.play_arrow_rounded,
                                  color: Colors.white,
                                  size: isSmallScreen ? 16 : 18,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  "شارك الآن",
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontWeight: FontWeight.bold,
                                    fontSize: isSmallScreen ? 12 : 13,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildErrorContainer(String errorMessage) {
    return Container(
      height: 100,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(15),
      ),
      alignment: Alignment.center,
      child: Text(
        context.tr(convertErrorCodeToLanguageKey(errorMessage))!,
        textAlign: TextAlign.center,
        style: TextStyle(
          fontSize: 16,
          color: Theme.of(context).colorScheme.onTertiary,
        ),
      ),
    );
  }

  Widget _buildEmptyContests() {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(15),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            Icons.event_available_outlined,
            size: 40,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 12),
          Text(
            'لا توجد مسابقات متاحة حالياً',
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Theme.of(context).colorScheme.onTertiary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'تفقد هذا القسم لاحقاً للمشاركة في المسابقات الجديدة',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onTertiary.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildHorizontalSections() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Section title with icon
        Padding(
          padding: EdgeInsets.symmetric(horizontal: hzMargin),
          child: Row(
            children: [
              Icon(
                Iconsax.flag, // Icon for challenges section
                color: Theme.of(context).primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                "قسم التحديات", // Changed text to "قسم التحديات"
                style: _boldTextStyle,
              ),
            ],
          ),
        ),
        const SizedBox(height: 15),

        // Scrollable horizontal section
        SizedBox(
          height: 160,
          child: ListView(
            physics: const BouncingScrollPhysics(),
            padding: EdgeInsets.symmetric(horizontal: hzMargin - 5),
            scrollDirection: Axis.horizontal,
            children: [
              // Battle items
              ...buildSectionItems(
                battleName,
                [Iconsax.people, Iconsax.profile_2user],
                battleDesc,
                (index) => _onPressedBattle(battleName[index]),
                primaryColor: Theme.of(context).primaryColor,
              ),

              // Exam and self-challenge items
              ...buildSectionItems(
                examSelf,
                [Iconsax.clipboard_text, Iconsax.medal],
                examSelfDesc,
                (index) => _onPressedSelfExam(examSelf[index]),
                primaryColor:
                    Theme.of(context).colorScheme.secondary != Colors.white
                        ? Theme.of(context).colorScheme.secondary
                        : Theme.of(context).primaryColor,
              ),
            ],
          ),
        )
      ],
    );
  }

  // Helper function to build section items
  List<Widget> buildSectionItems(List<String> names, List<IconData> icons,
      List<String> descriptions, Function(int) onTapCallback,
      {required Color primaryColor}) {
    return List.generate(
      names.length,
      (i) => GestureDetector(
        onTap: () => widget.isGuest ? _showLoginDialog() : onTapCallback(i),
        child: Container(
          width: 140,
          margin: const EdgeInsets.only(right: 12, bottom: 5),
          decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Theme.of(context).shadowColor.withOpacity(0.08),
                blurRadius: 10,
                spreadRadius: 0,
                offset: const Offset(0, 3),
              ),
            ],
            border: Border.all(
              color: Theme.of(context).dividerColor.withOpacity(0.05),
              width: 1,
            ),
          ),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Icon container with gradient background
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      primaryColor,
                      primaryColor.withOpacity(0.7),
                    ],
                  ),
                  shape: BoxShape.circle,
                  boxShadow: [
                    BoxShadow(
                      color: primaryColor.withOpacity(0.3),
                      blurRadius: 8,
                      spreadRadius: 0,
                      offset: const Offset(0, 3),
                    ),
                  ],
                ),
                child: Icon(
                  icons[i],
                  size: 26,
                  color: Colors.white,
                ),
              ),
              const SizedBox(height: 12),
              // Text with subtle shadow for better readability
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 8),
                child: Text(
                  context.tr(names[i])!,
                  textAlign: TextAlign.center,
                  style: TextStyle(
                    color: Theme.of(context).colorScheme.onTertiary,
                    fontWeight: FontWeight.bold,
                    fontSize: 13,
                    height: 1.2,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  String _userRank = '0';

  //String _userCoins = '0';
  String _userScore = '0';

  Widget _buildHome() {
    return Stack(
      children: [
        RefreshIndicator(
          color: Theme.of(context).primaryColor,
          backgroundColor: Theme.of(context).scaffoldBackgroundColor,
          onRefresh: () async {
            fetchUserDetails();

            _currLangId = UiUtils.getCurrentQuizLanguageId(context);
            final quizCubit = context.read<QuizCategoryCubit>();
            final quizZoneCubit = context.read<QuizoneCategoryCubit>();

            if (widget.isGuest) {
              await quizCubit.getQuizCategory(
                languageId: _currLangId,
                type: _quizZoneId,
              );
              await quizZoneCubit.getQuizCategory(languageId: _currLangId);
            } else {
              await quizCubit.getQuizCategoryWithUserId(
                languageId: _currLangId,
                type: _quizZoneId,
              );

              await quizZoneCubit.getQuizCategoryWithUserId(
                languageId: _currLangId,
              );
              await context
                  .read<ContestCubit>()
                  .getContest(languageId: _currLangId);
            }
            setState(() {});
          },
          child: ListView(
            children: [
              _buildProfileContainer(),
              UserAchievements(
                userRank: _userRank,
                userScore: _userScore,
              ),
              BlocBuilder<QuizoneCategoryCubit, QuizoneCategoryState>(
                builder: (context, state) {
                  if (state is QuizoneCategoryFailure &&
                      state.errorMessage == errorCodeDataNotFound) {
                    return const SizedBox.shrink();
                  }

                  if (_sysConfigCubit.isQuizZoneEnabled) {
                    return _buildCategory();
                  }

                  return const SizedBox.shrink();
                },
              ),
              if (_sysConfigCubit.isAdsEnable &&
                  _sysConfigCubit.isDailyAdsEnabled &&
                  !widget.isGuest) ...[
                //  _buildDailyAds(),
              ],
              if (_sysConfigCubit.isContestEnabled && !widget.isGuest) ...[
                _buildLiveContestSection(),
              ],
              const SizedBox(height: 20),
              _buildHorizontalSections(),
              const SizedBox(height: 30),
              _buildContactAndShareSection(),
              const SizedBox(height: 30),
            ],
          ),
        ),
        if (showUpdateContainer) const UpdateAppContainer(),
      ],
    );
  }

  void fetchUserDetails() {
    context.read<UserDetailsCubit>().fetchUserDetails();
  }

  bool profileComplete = false;

  final List<Widget> _navBodies = [];

  int _navIndex = 0;

  Widget buildHomeBody() {
    return SafeArea(
      child: widget.isGuest
          ? _buildHome()
          : BlocConsumer<UserDetailsCubit, UserDetailsState>(
              bloc: context.read<UserDetailsCubit>(),
              listener: (context, state) {
                if (state is UserDetailsFetchSuccess) {
                  UiUtils.fetchBookmarkAndBadges(
                    context: context,
                    userId: state.userProfile.userId!,
                  );
                  if (state.userProfile.profileUrl!.isEmpty ||
                      state.userProfile.name!.isEmpty) {
                    if (!profileComplete) {
                      profileComplete = true;

                      Navigator.of(context).pushNamed(
                        Routes.selectProfile,
                        arguments: false,
                      );
                    }
                    return;
                  }
                } else if (state is UserDetailsFetchFailure) {
                  if (state.errorMessage == errorCodeUnauthorizedAccess) {
                    showAlreadyLoggedInDialog(context);
                  }
                }
              },
              builder: (context, state) {
                if (state is UserDetailsFetchInProgress ||
                    state is UserDetailsInitial) {
                  return const Center(child: CircularProgressContainer());
                }
                if (state is UserDetailsFetchFailure) {
                  return Center(
                    child: ErrorContainer(
                      showBackButton: true,
                      errorMessage:
                          convertErrorCodeToLanguageKey(state.errorMessage),
                      onTapRetry: fetchUserDetails,
                      showErrorImage: true,
                    ),
                  );
                }

                final user = (state as UserDetailsFetchSuccess).userProfile;

                _userName = user.name!;
                _userProfileImg = user.profileUrl!;
                _userRank = user.allTimeRank!;
                //    _userCoins = user.coins!;
                _userScore = user.allTimeScore!;

                return _buildHome();
              },
            ),
    );
  }

  @override
  Widget build(BuildContext context) {
    /// need to add this here, cause textStyle doesn't update automatically when changing theme.
    _boldTextStyle = TextStyle(
      fontWeight: FontWeight.bold,
      fontSize: 18,
      color: Theme.of(context).colorScheme.onTertiary,
    );

    return Scaffold(
      body: _navBodies.isEmpty
          ? buildHomeBody()
          : IndexedStack(
              index: _navIndex,
              children: _navBodies,
            ),
      bottomNavigationBar: Container(
        // border
        decoration: BoxDecoration(
          color: Theme.of(context).dialogBackgroundColor,
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withOpacity(0.2),
              blurRadius: 6,
              offset: const Offset(0, 1),
            ),
          ],
        ),
        child: SalomonBottomBar(
          margin: EdgeInsets.only(
            left: hzMargin,
            right: hzMargin,
            bottom: 15,
            top: 12,
          ),
          onTap: (index) {
            if ((index == 1 || index == 2) && widget.isGuest) {
              _showLoginDialog();
              return;
            }
            setState(
              () {
                _navIndex = index;
              },
            );
          },
          currentIndex: _navIndex,
          unselectedItemColor: Theme.of(context).colorScheme.onTertiary,
          selectedItemColor:
              Theme.of(context).textSelectionTheme.selectionColor,
          items: [
            SalomonBottomBarItem(
              icon: const Icon(Icons.home_filled),
              title: Text(
                context.tr('homeBtn') ?? 'Home',
                style: TextStyle(
                  fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
                ),
              ),
            ),
            if (_sysConfigCubit.isQuizZoneEnabled)
              SalomonBottomBarItem(
                icon: const Icon(IconsaxPlusBold.category),
                title: Text(
                  context.tr('categoriesLbl') ?? 'Categories',
                  style: TextStyle(
                    fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
                  ),
                ),
              ),
            SalomonBottomBarItem(
              icon: const Icon(
                Icons.leaderboard_rounded,
              ),
              title: Text(
                context.tr('leaderboardLbl') ?? 'Leaderboard',
                style: TextStyle(
                  fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
                ),
              ),
            ),
            SalomonBottomBarItem(
              icon: const Icon(Icons.person),
              title: Text(
                context.tr('profileLbl') ?? 'Profile',
                style: TextStyle(
                  fontFamily: GoogleFonts.ibmPlexSansArabic().fontFamily,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildContactAndShareSection() {
    // حساب حجم الشاشة للتكيف مع مختلف الأجهزة
    final size = MediaQuery.of(context).size;
    final isTablet = size.width >= 600;

    // تحديد الهوامش بناءً على حجم الشاشة
    final horizontalPadding = isTablet ? size.width * 0.08 : hzMargin;

    // تحديد ما إذا كان يجب استخدام تخطيط عمودي أو أفقي
    final useVerticalLayout = size.width < 480;

    return Padding(
      padding: EdgeInsets.symmetric(horizontal: horizontalPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // عنوان القسم
          Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              children: [
                Icon(
                  Icons.support_agent,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  "تواصل معنا",
                  style: _boldTextStyle,
                ),
              ],
            ),
          ),

          // بطاقات التواصل
          useVerticalLayout
              ? Column(
                  children: [
                    _buildActionCard(
                      icon: FontAwesomeIcons.whatsapp,
                      title: "تواصل معنا",
                      subtitle: "لأي استفسارات أو الإقتراحات",
                      onTap: _openWhatsAppChat,
                      gradientColors: [
                        Theme.of(context).primaryColor.withOpacity(0.8),
                        Theme.of(context).primaryColor,
                      ],
                    ),
                    const SizedBox(height: 12),
                    _buildActionCard(
                      icon: Icons.share_rounded,
                      title: "شارك التطبيق",
                      subtitle: "مع أصدقائك",
                      onTap: _shareApp,
                      gradientColors: [
                        Theme.of(context)
                            .colorScheme
                            .secondary
                            .withOpacity(0.8),
                        Theme.of(context).colorScheme.secondary,
                      ],
                    ),
                  ],
                )
              : Row(
                  children: [
                    Expanded(
                      child: _buildActionCard(
                        icon: FontAwesomeIcons.whatsapp,
                        title: "تواصل معنا",
                        subtitle: "لأي استفسارات أو الإقتراحات",
                        onTap: _openWhatsAppChat,
                        gradientColors: [
                          Theme.of(context).primaryColor.withOpacity(0.8),
                          Theme.of(context).primaryColor,
                        ],
                      ),
                    ),
                    SizedBox(width: isTablet ? 20 : 12),
                    Expanded(
                      child: _buildActionCard(
                        icon: Icons.share_rounded,
                        title: "شارك التطبيق",
                        subtitle: "مع أصدقائك",
                        onTap: _shareApp,
                        gradientColors: [
                          Theme.of(context)
                              .colorScheme
                              .secondary
                              .withOpacity(0.8),
                          Theme.of(context).colorScheme.secondary,
                        ],
                      ),
                    ),
                  ],
                ),
        ],
      ),
    );
  }

  Widget _buildActionCard({
    required IconData icon,
    required String title,
    required String subtitle,
    required VoidCallback onTap,
    required List<Color> gradientColors,
  }) {
    final size = MediaQuery.of(context).size;
    final isSmallScreen = size.width < 360;
    final isTablet = size.width >= 600;
    final isLargeScreen = size.width >= 900;

    // تحديد الارتفاع بناءً على حجم الشاشة
    final cardHeight = isLargeScreen
        ? 120.0
        : (isTablet ? 110.0 : (isSmallScreen ? 90.0 : 100.0));

    // تحديد حجم الخط بناءً على حجم الشاشة
    final titleFontSize = isLargeScreen
        ? 18.0
        : (isTablet ? 17.0 : (isSmallScreen ? 14.0 : 16.0));

    final subtitleFontSize = isLargeScreen
        ? 14.0
        : (isTablet ? 13.0 : (isSmallScreen ? 11.0 : 12.0));

    // تحديد حجم الأيقونة بناءً على حجم الشاشة
    final iconSize = isLargeScreen
        ? 24.0
        : (isTablet ? 22.0 : (isSmallScreen ? 18.0 : 20.0));

    // تحديد حجم وسادة الأيقونة
    final iconPadding =
        isLargeScreen ? 14.0 : (isTablet ? 12.0 : (isSmallScreen ? 8.0 : 10.0));

    return Container(
      height: cardHeight,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Theme.of(context).shadowColor.withOpacity(0.08),
            blurRadius: 10,
            spreadRadius: 1,
            offset: const Offset(0, 4),
          ),
        ],
        gradient: LinearGradient(
          colors: gradientColors,
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: EdgeInsets.all(isTablet ? 16 : 12),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.all(iconPadding),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.05),
                        blurRadius: 5,
                        spreadRadius: 0,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: Colors.white,
                    size: iconSize,
                  ),
                ),
                SizedBox(width: isTablet ? 16 : 10),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        title,
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: titleFontSize,
                          fontWeight: FontWeight.bold,
                          height: 1.2,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                      SizedBox(height: isTablet ? 4 : 2),
                      Text(
                        subtitle,
                        style: TextStyle(
                          color: Colors.white.withOpacity(0.9),
                          fontSize: subtitleFontSize,
                          height: 1.2,
                        ),
                        overflow: TextOverflow.ellipsis,
                        maxLines: 2,
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  // Open WhatsApp chat with the support number
  Future<void> _openWhatsAppChat() async {
    const whatsappUrl = 'https://wa.me/9660581105341';

    try {
      if (await canLaunchUrlString(whatsappUrl)) {
        await launchUrlString(whatsappUrl,
            mode: LaunchMode.externalApplication);
      } else {
        const fallbackUrl = 'https://api.whatsapp.com/send?phone=9660581105341';
        if (await canLaunchUrlString(fallbackUrl)) {
          await launchUrlString(fallbackUrl,
              mode: LaunchMode.externalApplication);
        } else {
          if (!mounted) return;
          UiUtils.showSnackBar(
              'لا يمكن فتح واتساب. الرجاء التأكد من تثبيت التطبيق.', context);
        }
      }
    } catch (e) {
      log('Error opening WhatsApp: $e');
      if (!mounted) return;
      UiUtils.showSnackBar('حدث خطأ أثناء محاولة فتح واتساب.', context);
    }
  }

  // Share app functionality
  Future<void> _shareApp() async {
    try {
      const shareMessage =
          'تطبيق مجتهد - أفضل تطبيق للاختبارات والتدريب! 📚\n\n'
          'دورات واختبارات متنوعة تساعدك على التفوق في المذاكرة 🎓\n'
          'حمل التطبيق الآن! 📱\n\n'
          'https://mujtahidacademy.com';

      UiUtils.share(
        shareMessage,
        context: context,
      );
    } catch (e) {
      log('Error sharing app: $e');
      if (!mounted) return;

      UiUtils.showSnackBar(e.toString(), context);
    }
  }

  // Share app functionality
  // Future<void> _shareApp() async {
  //   try {
  //     UiUtils.share(
  //       '${context.read<SystemConfigCubit>().appUrl}\n${context.read<SystemConfigCubit>().shareAppText}',
  //       context: context,
  //     );
  //   } catch (e) {
  //     log('Error sharing app: $e');
  //     if (!mounted) return;

  //     UiUtils.showSnackBar(e.toString(), context);
  //   }
  // }
}

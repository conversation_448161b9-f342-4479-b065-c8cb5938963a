import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/app_localization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/localization/appLocalizationCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/utils/constants/assets_constants.dart';

class InitialLanguageSelectionScreen extends StatefulWidget {
  const InitialLanguageSelectionScreen({super.key});

  @override
  State<InitialLanguageSelectionScreen> createState() =>
      _InitialLanguageSelectionScreenState();

  static Route<dynamic> route() => CupertinoPageRoute(
        builder: (_) => const InitialLanguageSelectionScreen(),
      );
}

class _InitialLanguageSelectionScreenState
    extends State<InitialLanguageSelectionScreen> {
  @override
  void initState() {
    super.initState();
    _setLanguageAndProceed();
  }

  Future<void> _setLanguageAndProceed() async {
    // Add logging
    print("=== Starting Language Selection ===");

    final supportedLanguages =
        context.read<SystemConfigCubit>().getSupportedLanguageList();
    print(
        "Supported Languages: ${supportedLanguages.map((e) => e.name).toList()}");

    final defaultLanguage = supportedLanguages[0];
    print("Selected Default Language: ${defaultLanguage.name}");
    print("Is RTL: ${defaultLanguage.isRTL}");

    try {
      await AppLocalization().changeLanguage(defaultLanguage.name);
      print("Language changed successfully in AppLocalization");

      context.read<AppLocalizationCubit>().changeLanguage(
            defaultLanguage.name,
            isRTL: defaultLanguage.isRTL,
          );
      print("Language changed successfully in AppLocalizationCubit");

      print("Navigating to Intro Slider...");
      await Navigator.of(context).pushReplacementNamed(Routes.introSlider);
    } catch (e) {
      print("Error during language setup: $e");
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Stack(
        children: [
          // Background gradient - نفس تصميم شاشة البداية
          Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Theme.of(context).primaryColor.withOpacity(0.8),
                  Theme.of(context).scaffoldBackgroundColor,
                ],
              ),
            ),
          ),

          // Logo in center
          Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Logo Container - نفس التصميم
                Container(
                  padding: const EdgeInsets.all(30),
                  decoration: BoxDecoration(
                    color: Colors.white.withOpacity(0.2),
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        spreadRadius: 2,
                      ),
                    ],
                  ),
                  child: QImage(
                    imageUrl: Assets
                        .splashLogo, // نفس اللوجو المستخدم في splash_screen
                    width: 120,
                    height: 120,
                    fit: BoxFit.contain,
                    padding: EdgeInsets.zero,
                    color: Theme.of(context).scaffoldBackgroundColor,
                  ),
                ),
              ],
            ),
          ),

          // Loading indicator
          Positioned(
            bottom: MediaQuery.of(context).size.height * 0.15,
            left: 0,
            right: 0,
            child: Center(
              child: Container(
                width: 50,
                height: 50,
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.white.withOpacity(0.9),
                  borderRadius: BorderRadius.circular(25),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 15,
                      spreadRadius: 2,
                    ),
                  ],
                ),
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Theme.of(context).primaryColor,
                  ),
                  strokeWidth: 3,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
// import 'package:flutter/cupertino.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_bloc/flutter_bloc.dart';
// import 'package:flutterquiz/app/app_localization.dart';
// import 'package:flutterquiz/app/routes.dart';
// import 'package:flutterquiz/features/localization/appLocalizationCubit.dart';
// import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
// import 'package:flutterquiz/ui/widgets/all.dart';
// import 'package:flutterquiz/utils/extensions.dart';
// import 'package:flutterquiz/utils/ui_utils.dart';
//
// class InitialLanguageSelectionScreen extends StatefulWidget {
//   const InitialLanguageSelectionScreen({super.key});
//
//   @override
//   State<InitialLanguageSelectionScreen> createState() =>
//       _InitialLanguageSelectionScreenState();
//
//   static Route<dynamic> route() => CupertinoPageRoute(
//         builder: (_) => const InitialLanguageSelectionScreen(),
//       );
// }
//
// class _InitialLanguageSelectionScreenState
//     extends State<InitialLanguageSelectionScreen> {
//   late final _supportedLanguages =
//       context.read<SystemConfigCubit>().getSupportedLanguageList();
//
//   @override
//   Widget build(BuildContext context) {
//     return BlocBuilder<AppLocalizationCubit, AppLocalizationState>(
//       builder: (context, state) {
//         var currLang = state.language;
//
//         final size = MediaQuery.sizeOf(context);
//
//         return Scaffold(
//           appBar: QAppBar(
//             automaticallyImplyLeading: false,
//             title: Text(context.tr('selectLanguage')!),
//             usePrimaryColor: true,
//           ),
//           body: Padding(
//             padding: EdgeInsets.symmetric(
//               vertical: size.height * UiUtils.vtMarginPct,
//               horizontal: size.width * UiUtils.hzMarginPct,
//             ),
//             child: ListView.separated(
//               itemBuilder: (context, i) {
//                 final language = _supportedLanguages[i];
//
//                 return Container(
//                   decoration: BoxDecoration(
//                     border: Border.all(
//                       color: Theme.of(context).primaryColor.withOpacity(.7),
//                     ),
//                     borderRadius: BorderRadius.circular(10),
//                   ),
//                   child: RadioListTile(
//                     toggleable: true,
//                     activeColor: currLang == language.name
//                         ? Theme.of(context).primaryColor
//                         : Colors.white,
//                     title: Text(
//                       language.name,
//                       style: TextStyle(
//                         fontWeight: FontWeight.bold,
//                         fontSize: 18,
//                         color: Theme.of(context).colorScheme.onTertiary,
//                       ),
//                     ),
//                     value: language.name,
//                     groupValue: currLang,
//                     onChanged: (value) async {
//                       if (value == null) return;
//
//                       currLang = value;
//
//                       if (state.language != language.name) {
//                         await AppLocalization().changeLanguage(language.name);
//                         context.read<AppLocalizationCubit>().changeLanguage(
//                               language.name,
//                               isRTL: language.isRTL,
//                             );
//                         setState(() {});
//                       }
//                     },
//                   ),
//                 );
//               },
//               separatorBuilder: (_, i) =>
//                   const SizedBox(height: UiUtils.listTileGap),
//               itemCount: _supportedLanguages.length,
//             ),
//           ),
//           floatingActionButton: _confirmAndContinueButton,
//         );
//       },
//     );
//   }
//
//   FloatingActionButton get _confirmAndContinueButton => FloatingActionButton(
//         onPressed: () => Navigator.of(context).pushReplacementNamed(
//           Routes.introSlider,
//         ),
//         backgroundColor: Theme.of(context).primaryColor,
//         foregroundColor: Theme.of(context).colorScheme.surface,
//         child: const Icon(Icons.check),
//       );
// }

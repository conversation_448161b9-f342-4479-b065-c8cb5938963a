import 'dart:developer';
import 'dart:io';
import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutterquiz/StoreConfig.dart';
import 'package:flutterquiz/app/app.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/performance_utils.dart';
import 'package:purchases_flutter/purchases_flutter.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';
import 'firebase_options.dart';

void main() {
  runZonedGuarded(() async {
    WidgetsFlutterBinding.ensureInitialized();

    try {
      // عرض شاشة التحميل بنفس تصميم صفحة "من نحن"
      runApp(
        MaterialApp(
          debugShowCheckedModeBanner: false,
          theme: ThemeData(
            primaryColor: const Color(0xFF2196F3), // اللون الأساسي
            scaffoldBackgroundColor: Colors.white,
          ),
          home: Builder(
            builder: (context) => Scaffold(
              body: Stack(
                children: [
                  // Background gradient - نفس التصميم
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          Theme.of(context).primaryColor.withOpacity(0.8),
                          Theme.of(context).scaffoldBackgroundColor,
                        ],
                      ),
                    ),
                  ),

                  // Logo in center
                  Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        // Logo Container - نفس التصميم
                        Container(
                          padding: const EdgeInsets.all(30),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.2),
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 20,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: Image.asset(
                            'assets/images/app_logo.png', // اللوجو
                            width: 120,
                            height: 120,
                            fit: BoxFit.contain,
                            color: Theme.of(context).scaffoldBackgroundColor,
                            errorBuilder: (context, error, stackTrace) {
                              return Icon(
                                Icons.apps,
                                size: 120,
                                color: Theme.of(context).scaffoldBackgroundColor,
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Loading indicator
                  Positioned(
                    bottom: MediaQuery.of(context).size.height * 0.15,
                    left: 0,
                    right: 0,
                    child: Center(
                      child: Container(
                        width: 50,
                        height: 50,
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.9),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.1),
                              blurRadius: 15,
                              spreadRadius: 2,
                            ),
                          ],
                        ),
                        child: CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Theme.of(context).primaryColor,
                          ),
                          strokeWidth: 3,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      );

      // تهيئة Firebase أولاً (مع التحقق من عدم التهيئة المسبقة)
      if (!kIsWeb) {
        try {
          // التحقق من وجود Firebase apps مُهيأة مسبقاً
          if (Firebase.apps.isEmpty) {
            await Firebase.initializeApp(
              options: DefaultFirebaseOptions.currentPlatform,
            );
            log('✅ تم تهيئة Firebase بنجاح');
          } else {
            log('ℹ️ Firebase مُهيأ مسبقاً');
          }
        } catch (e) {
          if (e.toString().contains('duplicate-app')) {
            log('ℹ️ Firebase مُهيأ مسبقاً - تجاهل الخطأ');
          } else {
            log('❌ خطأ في تهيئة Firebase: $e');
            rethrow;
          }
        }
      }

      // تطبيق تحسينات الأداء
      await PerformanceUtils.applyAllOptimizations();

      // تهيئة Store Configuration
      _initializeStore();

      // تهيئة RevenueCat SDK
      await _configureSDK();

      // تهيئة التطبيق الرئيسي
      final app = await initializeApp();
      runApp(app);
    } catch (e, stack) {
      log('Error during initialization: $e');
      log('Stack trace: $stack');
      _handleStartupError(e, stack);
    }
  }, (error, stack) {
    log('Uncaught error: $error');
    log('Stack trace: $stack');
  });
}

void _initializeStore() {
  try {
    String apiKey;
    Store store;

    if (Platform.isIOS || Platform.isMacOS) {
      apiKey = appleApiKey;
      store = Store.appStore;
      log('🍎 تهيئة متجر Apple مع API Key: ${apiKey.substring(0, 10)}...');
    } else if (Platform.isAndroid) {
      const useAmazon = bool.fromEnvironment("amazon");
      apiKey = useAmazon ? googleApiKey : googleApiKey;
      store = useAmazon ? Store.amazon : Store.playStore;
      log('🤖 تهيئة متجر ${useAmazon ? "Amazon" : "Google Play"} مع API Key: ${apiKey.substring(0, 10)}...');
    } else {
      log('⚠️ منصة غير مدعومة للمتجر');
      return;
    }

    // التحقق من صحة API Key
    if (apiKey.isEmpty) {
      log('❌ خطأ: API Key فارغ للمنصة ${Platform.operatingSystem}');
      return;
    }

    // إنشاء StoreConfig
    StoreConfig(store: store, apiKey: apiKey);
    log('✅ تم تهيئة StoreConfig بنجاح');
  } catch (e, stack) {
    log('❌ خطأ في تهيئة Store Config: $e');
    log('Stack trace: $stack');
  }
}

void _handleStartupError(Object error, StackTrace stackTrace) {
  runApp(MaterialApp(
    debugShowCheckedModeBanner: false,
    home: Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Color(0xFFFFEBEE),
              Colors.white,
              Color(0xFFFFCDD2),
            ],
          ),
        ),
        child: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 64),
                const SizedBox(height: 24),
                const Text(
                  'عذراً، حدث خطأ غير متوقع',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: Colors.red,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                const Text(
                  'يرجى إعادة تشغيل التطبيق',
                  style: TextStyle(fontSize: 16, color: Colors.grey),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.grey[100],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    error.toString(),
                    style: const TextStyle(fontSize: 12, color: Colors.grey),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    ),
  ));
}

Future<void> _configureSDK() async {
  try {
    // التحقق من تهيئة StoreConfig أولاً
    if (!StoreConfig.isInitialized) {
      log('❌ خطأ: StoreConfig لم يتم تهيئته');
      return;
    }

    // التحقق من وجود API Key
    if (StoreConfig.instance.apiKey.isEmpty) {
      log('❌ خطأ: API Key فارغ');
      return;
    }

    await Purchases.setLogLevel(LogLevel.verbose);

    log('🔄 بدء تهيئة RevenueCat...');
    log('🔑 API Key: ${StoreConfig.instance.apiKey}');
    log('🏪 Store: ${StoreConfig.instance.store}');

    // التكوين البسيط - الإصدار الجديد
    final configuration = PurchasesConfiguration(StoreConfig.instance.apiKey);
    await Purchases.configure(configuration);

    log('✅ تم تهيئة RevenueCat بنجاح');

    if (Platform.isIOS) {
      log('📱 جهاز iOS - جاري مزامنة المشتريات...');
      await Purchases.syncPurchases();
      log('✅ تم مزامنة المشتريات بنجاح');
    }
  } catch (e, stack) {
    log('❌ خطأ في تهيئة RevenueCat: $e');
    log('Stack trace: $stack');
    // لا نرمي الخطأ هنا لأن التطبيق يمكن أن يعمل بدون RevenueCat
  }
}

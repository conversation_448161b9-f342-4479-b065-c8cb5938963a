import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/quiz/cubits/quizzone_category_cubit.dart';
import 'package:flutterquiz/features/quiz/models/category.dart';
import 'package:flutterquiz/features/quiz/models/supercategory.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/ui/widgets/circularProgressContainer.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/ui/widgets/login_dialog.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:iconsax/iconsax.dart';

class SuperCategoryQuizZone extends StatelessWidget {
  final bool isGuest;

  Future<void> _showLoginDialog(BuildContext context) {
    return showLoginDialog(
      context,
      onTapYes: () {
        Navigator.of(context).pop();
        Navigator.of(context).pushNamed(Routes.login);
      },
    );
  }

  const SuperCategoryQuizZone({super.key, required this.isGuest});

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<QuizoneCategoryCubit, QuizoneCategoryState>(
      bloc: context.read<QuizoneCategoryCubit>(),
      listener: (context, state) {
        if (state is QuizoneCategoryFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      builder: (context, state) {
        if (state is QuizoneCategoryFailure) {
          return ErrorContainer(
            showRTryButton: false,
            showBackButton: false,
            showErrorImage: false,
            errorMessage: convertErrorCodeToLanguageKey(state.errorMessage),
            onTapRetry: () {
              context.read<QuizoneCategoryCubit>().getQuizCategoryWithUserId(
                    languageId: UiUtils.getCurrentQuizLanguageId(context),
                  );
            },
          );
        }

        if (state is QuizoneCategorySuccess) {
          final categories = state.categories;
          final superCategories = _groupCategoriesBySuper(categories);

          return Column(
            children: [
              // عرض التصنيفات الفائقة
              SizedBox(
                height: 120,
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  scrollDirection: Axis.horizontal,
                  physics: const BouncingScrollPhysics(),
                  itemCount: superCategories.length,
                  itemBuilder: (context, index) {
                    final superCategory = superCategories.keys.elementAt(index);
                    final categoriesInSuper = superCategories[superCategory]!;

                    return SuperCategoryCard(
                      superCategory: superCategory,
                      categoriesCount: categoriesInSuper.length,
                      onTap: () {
                        if (isGuest) {
                          _showLoginDialog(context);
                          return;
                        }

                        _showCategoriesBottomSheet(
                            context, superCategory, categoriesInSuper);
                      },
                    );
                  },
                ),
              ),

              const SizedBox(height: 20),

              // عرض بعض الفئات المميزة
              if (categories.isNotEmpty) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 15),
                  child: Row(
                    children: [
                      Icon(
                        Iconsax.star,
                        color: Theme.of(context).primaryColor,
                        size: 18,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'الفئات المميزة',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onTertiary,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: 10),
                SizedBox(
                  height: 160,
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 15),
                    scrollDirection: Axis.horizontal,
                    physics: const BouncingScrollPhysics(),
                    itemCount: categories.take(5).length,
                    itemBuilder: (context, index) {
                      final category = categories[index];
                      return CategoryCard(
                        category: category,
                        onTap: () => _navigateToCategory(context, category),
                      );
                    },
                  ),
                ),
              ],
            ],
          );
        }

        return const Center(child: CircularProgressContainer());
      },
    );
  }

  Map<SuperCategory, List<Category>> _groupCategoriesBySuper(
      List<Category> categories) {
    final superCategories = SuperCategory.getDefaultSuperCategories();
    final Map<SuperCategory, List<Category>> grouped = {};

    // تهيئة المجموعات
    for (final superCat in superCategories) {
      grouped[superCat] = [];
    }

    // تصنيف الفئات
    for (final category in categories) {
      final superCategoryId =
          SuperCategory.classifyCategory(category.categoryName ?? '');
      final superCategory = superCategories.firstWhere(
        (sc) => sc.id == superCategoryId,
        orElse: () => superCategories.first,
      );
      grouped[superCategory]!.add(category);
    }

    // إزالة التصنيفات الفارغة
    grouped.removeWhere((key, value) => value.isEmpty);

    return grouped;
  }

  void _showCategoriesBottomSheet(
    BuildContext context,
    SuperCategory superCategory,
    List<Category> categories,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          decoration: BoxDecoration(
            color: Theme.of(context).scaffoldBackgroundColor,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
          ),
          child: Column(
            children: [
              // Handle bar
              Container(
                margin: const EdgeInsets.symmetric(vertical: 10),
                height: 4,
                width: 40,
                decoration: BoxDecoration(
                  color: Theme.of(context).dividerColor,
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              // Header
              Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    _getSuperCategoryIcon(context, superCategory.icon),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            superCategory.name,
                            style: TextStyle(
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).colorScheme.onTertiary,
                            ),
                          ),
                          Text(
                            superCategory.description,
                            style: TextStyle(
                              fontSize: 14,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onTertiary
                                  .withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // Categories grid
              Expanded(
                child: GridView.builder(
                  controller: scrollController,
                  padding: const EdgeInsets.symmetric(horizontal: 20),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 1.2,
                    crossAxisSpacing: 15,
                    mainAxisSpacing: 15,
                  ),
                  itemCount: categories.length,
                  itemBuilder: (context, index) {
                    final category = categories[index];
                    return CategoryGridCard(
                      category: category,
                      onTap: () {
                        Navigator.pop(context);
                        _navigateToCategory(context, category);
                      },
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getSuperCategoryIcon(BuildContext context, String iconName) {
    IconData iconData;
    switch (iconName) {
      case 'brain':
        iconData = Iconsax.book;
        break;
      case 'book':
        iconData = Iconsax.book;
        break;
      case 'graduation_cap':
        iconData = Iconsax.teacher;
        break;
      case 'language':
        iconData = Iconsax.language_square;
        break;
      case 'certificate':
        iconData = Iconsax.award;
        break;
      default:
        iconData = Iconsax.category;
    }

    return Icon(
      iconData,
      size: 24,
      color: Theme.of(context).primaryColor,
    );
  }

  void _navigateToCategory(BuildContext context, Category category) {
    if (isGuest) {
      _showLoginDialog(context);
      return;
    }

    if (category.noOf == '0') {
      if (category.maxLevel == '0') {
        Navigator.of(context).pushNamed(
          Routes.quiz,
          arguments: {
            'numberOfPlayer': 1,
            'quizType': QuizTypes.quizZone,
            'categoryId': category.id,
            'subcategoryId': '',
            'level': '0',
            'subcategoryMaxLevel': '0',
            'unlockedLevel': 0,
            'contestId': '',
            'comprehensionId': '',
            'quizName': 'Quiz Zone',
            'showRetryButton': category.noOfQues! != '0',
          },
        );
      } else {
        Navigator.of(context).pushNamed(
          Routes.levels,
          arguments: {
            'Category': category,
          },
        );
      }
    } else {
      Navigator.of(context).pushNamed(
        Routes.subcategoryAndLevel,
        arguments: {
          'category_id': category.id,
          'category_name': category.categoryName,
          'isPremiumCategory': category.isPremium,
        },
      );
    }
  }
}

class SuperCategoryCard extends StatelessWidget {
  final SuperCategory superCategory;
  final int categoriesCount;
  final VoidCallback onTap;

  const SuperCategoryCard({
    super.key,
    required this.superCategory,
    required this.categoriesCount,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 160,
        margin: const EdgeInsets.only(right: 15),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.1),
              Theme.of(context).primaryColor.withOpacity(0.2),
            ],
          ),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.3),
          ),
        ),
        child: Padding(
          padding: const EdgeInsets.all(15),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  _getSuperCategoryIcon(context, superCategory.icon),
                  const Spacer(),
                  Container(
                    padding:
                        const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    // child: Text(
                    //   '$categoriesCount',
                    //   style: TextStyle(
                    //     color: Theme.of(context).primaryColor,
                    //     fontSize: 12,
                    //     fontWeight: FontWeight.bold,
                    //   ),
                    // ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                superCategory.name,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: Theme.of(context).colorScheme.onTertiary,
                ),
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              // Text(
              //   '${categoriesCount} فئة',
              //   style: TextStyle(
              //     fontSize: 12,
              //     color:
              //         Theme.of(context).colorScheme.onTertiary.withOpacity(0.6),
              //   ),
              // ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _getSuperCategoryIcon(BuildContext context, String iconName) {
    IconData iconData;
    switch (iconName) {
      case 'brain':
        iconData = Iconsax.book;
        break;
      case 'book':
        iconData = Iconsax.book;
        break;
      case 'graduation_cap':
        iconData = Iconsax.teacher;
        break;
      case 'language':
        iconData = Iconsax.language_square;
        break;
      case 'certificate':
        iconData = Iconsax.award;
        break;
      default:
        iconData = Iconsax.category;
    }

    return Icon(
      iconData,
      size: 20,
      color: Theme.of(context).primaryColor,
    );
  }
}

class CategoryCard extends StatelessWidget {
  final Category category;
  final VoidCallback onTap;

  const CategoryCard({
    super.key,
    required this.category,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 200,
        margin: const EdgeInsets.only(right: 15),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              Theme.of(context).primaryColor.withOpacity(0.1),
              Theme.of(context).primaryColor.withOpacity(0.2),
            ],
          ),
          borderRadius: BorderRadius.circular(15),
          border: Border.all(
            color: Theme.of(context).primaryColor.withOpacity(0.1),
          ),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Stack(
            children: [
              // صورة الخلفية
              Positioned.fill(
                child: QImage(
                  imageUrl: category.image!,
                  fit: BoxFit.cover,
                  padding: EdgeInsets.zero,
                ),
              ),

              // تدرج لوني فوق الصورة
              Positioned.fill(
                child: DecoratedBox(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.transparent,
                        Colors.black.withOpacity(0.8),
                      ],
                    ),
                  ),
                ),
              ),

              // المحتوى
              Positioned(
                bottom: 12,
                left: 12,
                right: 12,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.categoryName!,
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 4,
                          ),
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.8),
                            borderRadius: BorderRadius.circular(20),
                          ),
                          child: Text(
                            '${category.noOfQues} سؤال',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class CategoryGridCard extends StatelessWidget {
  final Category category;
  final VoidCallback onTap;

  const CategoryGridCard({
    super.key,
    required this.category,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(15),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).shadowColor.withOpacity(0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(15),
          child: Column(
            children: [
              // صورة الفئة
              Expanded(
                flex: 3,
                child: Container(
                  width: double.infinity,
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Theme.of(context).primaryColor.withOpacity(0.1),
                        Theme.of(context).primaryColor.withOpacity(0.2),
                      ],
                    ),
                  ),
                  child: QImage(
                    imageUrl: category.image!,
                    fit: BoxFit.cover,
                    padding: EdgeInsets.zero,
                  ),
                ),
              ),

              // معلومات الفئة
              Expanded(
                flex: 2,
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        category.categoryName!,
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).colorScheme.onTertiary,
                        ),
                      ),
                      const Spacer(),
                      Row(
                        children: [
                          Icon(
                            Iconsax.document_text,
                            size: 14,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '${category.noOfQues} سؤال',
                            style: TextStyle(
                              fontSize: 12,
                              color: Theme.of(context)
                                  .colorScheme
                                  .onTertiary
                                  .withOpacity(0.7),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class SuperCategory {
  final String id;
  final String name;
  final String description;
  final String icon;
  final List<String> categoryIds;

  const SuperCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.categoryIds,
  });

  static List<SuperCategory> getDefaultSuperCategories() {
    return [
      SuperCategory(
        id: 'qudurat',
        name: 'قدرات',
        description: 'اختبارات القدرات العامة',
        icon: 'brain',
        categoryIds: [], // سيتم تحديدها بناءً على أسماء الفئات
      ),
      SuperCategory(
        id: 'tahsili',
        name: 'تحصيلي',
        description: 'الاختبارات التحصيلية',
        icon: 'book',
        categoryIds: [],
      ),
      SuperCategory(
        id: 'university_aptitude',
        name: 'قدرات جامعيين أو القدرة المعرفية',
        description: 'اختبارات القدرات للجامعيين والقدرة المعرفية',
        icon: 'graduation_cap',
        categoryIds: [],
      ),
      SuperCategory(
        id: 'english_tests',
        name: 'اختبارات الإنجليزي',
        description: 'اختبارات اللغة الإنجليزية المختلفة',
        icon: 'language',
        categoryIds: [],
      ),
      SuperCategory(
        id: 'professional_license',
        name: 'الرخصة المهنية',
        description: 'اختبارات الرخصة المهنية للمعلمين',
        icon: 'certificate',
        categoryIds: [],
      ),
    ];
  }

  /// تصنيف الفئة إلى التصنيف الفائق المناسب
  static String classifyCategory(String categoryName) {
    final name = categoryName.toLowerCase().trim();
    
    // قدرات
    if (name.contains('قدرات') && !name.contains('جامعي') && !name.contains('معرفي')) {
      return 'qudurat';
    }
    
    // تحصيلي
    if (name.contains('تحصيلي') || name.contains('تحصيل')) {
      return 'tahsili';
    }
    
    // قدرات جامعيين أو القدرة المعرفية
    if (name.contains('جامعي') || name.contains('معرفي') || 
        (name.contains('قدرات') && (name.contains('جامعة') || name.contains('كلية')))) {
      return 'university_aptitude';
    }
    
    // اختبارات الإنجليزي
    if (name.contains('إنجليزي') || name.contains('انجليزي') || 
        name.contains('english') || name.contains('ielts') || 
        name.contains('toefl') || name.contains('step')) {
      return 'english_tests';
    }
    
    // الرخصة المهنية
    if (name.contains('رخصة') || name.contains('مهنية') || 
        name.contains('معلم') || name.contains('تعليم')) {
      return 'professional_license';
    }
    
    // افتراضي: قدرات
    return 'qudurat';
  }
}
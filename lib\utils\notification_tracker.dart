// ignore_for_file: public_member_api_docs, sort_constructors_first
import 'dart:developer';
import 'package:shared_preferences/shared_preferences.dart';

/// NotificationTracker helps prevent duplicate notifications
/// by tracking processed notification IDs and timestamps
class NotificationTracker {
  static const String _processedNotificationsKey = 'processed_notifications';
  static const String _lastNotificationTimeKey = 'last_notification_time';
  static const int _duplicateThresholdMs = 5000; // 5 seconds
  
  /// Check if a notification should be processed
  /// Returns false if it's a duplicate within the threshold time
  static Future<bool> shouldProcessNotification({
    required String notificationId,
    required String type,
    String? title,
    String? body,
  }) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final now = DateTime.now().millisecondsSinceEpoch;
      
      // Create a unique identifier for this notification
      final uniqueId = _createUniqueId(notificationId, type, title, body);
      
      // Get processed notifications list
      final processedList = prefs.getStringList(_processedNotificationsKey) ?? [];
      
      // Get last notification time
      final lastTime = prefs.getInt(_lastNotificationTimeKey) ?? 0;
      
      // Check if this notification was recently processed
      if (processedList.contains(uniqueId)) {
        final timeDiff = now - lastTime;
        if (timeDiff < _duplicateThresholdMs) {
          log('Duplicate notification blocked: $uniqueId (${timeDiff}ms ago)');
          return false;
        }
      }
      
      // Mark this notification as processed
      await _markAsProcessed(uniqueId, now);
      return true;
    } catch (e) {
      log('Error in notification tracking: $e');
      return true; // Allow notification if tracking fails
    }
  }
  
  /// Create a unique identifier for the notification
  static String _createUniqueId(String id, String type, String? title, String? body) {
    return '${id}_${type}_${title ?? ''}_${body ?? ''}'.hashCode.toString();
  }
  
  /// Mark a notification as processed
  static Future<void> _markAsProcessed(String uniqueId, int timestamp) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      // Get current list
      final processedList = prefs.getStringList(_processedNotificationsKey) ?? [];
      
      // Add new ID (keep only last 50 to prevent memory issues)
      processedList.add(uniqueId);
      if (processedList.length > 50) {
        processedList.removeRange(0, processedList.length - 50);
      }
      
      // Save updated list and timestamp
      await prefs.setStringList(_processedNotificationsKey, processedList);
      await prefs.setInt(_lastNotificationTimeKey, timestamp);
    } catch (e) {
      log('Error marking notification as processed: $e');
    }
  }
  
  /// Clear all tracked notifications (useful for debugging)
  static Future<void> clearTrackedNotifications() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_processedNotificationsKey);
      await prefs.remove(_lastNotificationTimeKey);
      log('Notification tracking cleared');
    } catch (e) {
      log('Error clearing notification tracking: $e');
    }
  }
  
  /// Get statistics about tracked notifications
  static Future<Map<String, dynamic>> getTrackingStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final processedList = prefs.getStringList(_processedNotificationsKey) ?? [];
      final lastTime = prefs.getInt(_lastNotificationTimeKey) ?? 0;
      
      return {
        'total_tracked': processedList.length,
        'last_notification_time': DateTime.fromMillisecondsSinceEpoch(lastTime),
        'processed_ids': processedList,
      };
    } catch (e) {
      log('Error getting tracking stats: $e');
      return {};
    }
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/app/app_localization.dart';
import 'package:flutterquiz/app/routes.dart';
import 'package:flutterquiz/features/auth/cubits/authCubit.dart';
import 'package:flutterquiz/features/settings/settingsCubit.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/widgets/custom_image.dart';
import 'package:flutterquiz/ui/widgets/errorContainer.dart';
import 'package:flutterquiz/utils/constants/constants.dart';
import 'package:flutterquiz/utils/gdpr_helper.dart';
import 'package:unity_ads_plugin/unity_ads_plugin.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoAnimationController;
  late AnimationController _backgroundAnimationController;
  late AnimationController _glowAnimationController;
  late Animation<double> _logoScaleUpAnimation;
  late Animation<double> _fadeInAnimation;

  bool _systemConfigLoaded = false;

  final _appLogoPath = Assets.splashLogo;
  // final _orgLogoPath = Assets.orgLogo;
  final showCompanyLogo = false;

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _fetchSystemConfig();
    _loadLanguage();
  }

  @override
  void dispose() {
    // إيقاف جميع الـ animations قبل التخلص منها
    _logoAnimationController.stop();
    _backgroundAnimationController.stop();
    _glowAnimationController.stop();

    _logoAnimationController.dispose();
    _backgroundAnimationController.dispose();
    _glowAnimationController.dispose();
    super.dispose();
  }

  void _initAnimations() {
    _logoAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 2500),
    )..addListener(() {
        if (_logoAnimationController.isCompleted) {
          _navigateToNextScreen();
        }
      });

    _backgroundAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 3000),
    )..repeat();

    _glowAnimationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1500),
    )..repeat(reverse: true);

    _logoScaleUpAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoAnimationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOutBack),
      ),
    );

    _fadeInAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _logoAnimationController,
        curve: const Interval(0.3, 0.8, curve: Curves.easeIn),
      ),
    );

    _logoAnimationController.forward();
    _backgroundAnimationController.repeat();
    _glowAnimationController.repeat(reverse: true);
  }

  Future<void> _initUnityAds() async {
    final gameId = context.read<SystemConfigCubit>().unityGameId;

    // التحقق من صحة Game ID قبل التهيئة
    if (gameId.isEmpty ||
        gameId == 'Android Game Id' ||
        gameId == 'IOS Game Id') {
      print('Unity Ads: Invalid Game ID, skipping initialization');
      return;
    }

    try {
      await UnityAds.init(
        gameId: gameId,
        testMode: true,
        onComplete: () => print('Unity Ads: Initialized successfully'),
        onFailed: (err, msg) =>
            print('Unity Ads: Initialization Failed: $err $msg'),
      );
    } catch (e) {
      print('Unity Ads: Exception during initialization: $e');
    }
  }

  Future<void> _fetchSystemConfig() async {
    await context.read<SystemConfigCubit>().getSystemConfig();
    await GdprHelper.initialize();
  }

  Future<void> _loadLanguage() async {
    await AppLocalization().loadLanguage();
  }

  Future<void> _navigateToNextScreen() async {
    if (!_systemConfigLoaded) return;

    await _initUnityAds();

    final showIntroSlider =
        context.read<SettingsCubit>().state.settingsModel!.showIntroSlider;
    final currAuthState = context.read<AuthCubit>().state;

    if (showIntroSlider) {
      await Navigator.of(context).pushReplacementNamed(Routes.languageSelect);
      return;
    }

    if (currAuthState is Authenticated) {
      await Navigator.of(context).pushReplacementNamed(
        Routes.home,
        arguments: false,
      );
    } else {
      await Navigator.of(context).pushReplacementNamed(
        Routes.home,
        arguments: true,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocConsumer<SystemConfigCubit, SystemConfigState>(
      bloc: context.read<SystemConfigCubit>(),
      listener: (context, state) {
        if (state is SystemConfigFetchSuccess) {
          if (!_systemConfigLoaded) {
            _systemConfigLoaded = true;
          }

          if (_logoAnimationController.isCompleted) {
            _navigateToNextScreen();
          }
        }
      },
      builder: (context, state) {
        if (state is SystemConfigFetchFailure) {
          return Scaffold(
            backgroundColor: Theme.of(context).scaffoldBackgroundColor,
            body: Center(
              key: const Key('errorContainer'),
              child: ErrorContainer(
                showBackButton: true,
                errorMessageColor: Theme.of(context).colorScheme.onTertiary,
                errorMessage: convertErrorCodeToLanguageKey(state.errorCode),
                onTapRetry: () {
                  setState(_initAnimations);
                  _fetchSystemConfig();
                  _loadLanguage();
                },
                showErrorImage: true,
              ),
            ),
          );
        }

        final size = MediaQuery.of(context).size;

        return Scaffold(
          body: Stack(
            children: [
              // Background gradient
              Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Theme.of(context).primaryColor.withOpacity(0.8),
                      Theme.of(context).scaffoldBackgroundColor,
                    ],
                  ),
                ),
              ),

              // Logo and text in center
              Center(
                child: AnimatedBuilder(
                  animation: _logoAnimationController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: _logoScaleUpAnimation.value,
                      child: FadeTransition(
                        opacity: _fadeInAnimation,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            // Logo Container - أيقونة أكبر بدون نصوص
                            Container(
                              padding: const EdgeInsets.all(30),
                              decoration: BoxDecoration(
                                color: Colors.white.withOpacity(0.2),
                                shape: BoxShape.circle,
                                boxShadow: [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 20,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: QImage(
                                imageUrl: _appLogoPath,
                                width: 120,
                                height: 120,
                                fit: BoxFit.contain,
                                padding: EdgeInsets.zero,
                                color:
                                    Theme.of(context).scaffoldBackgroundColor,
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),

              // Loading indicator
              Positioned(
                bottom: MediaQuery.of(context).size.height * 0.15,
                left: 0,
                right: 0,
                child: AnimatedBuilder(
                  animation: _logoAnimationController,
                  builder: (context, child) {
                    return FadeTransition(
                      opacity: _fadeInAnimation,
                      child: Center(
                        child: Container(
                          width: 50,
                          height: 50,
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(25),
                            boxShadow: [
                              BoxShadow(
                                color: Colors.black.withOpacity(0.1),
                                blurRadius: 15,
                                spreadRadius: 2,
                              ),
                            ],
                          ),
                          child: CircularProgressIndicator(
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Theme.of(context).primaryColor,
                            ),
                            strokeWidth: 3,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        );
      },
    );
  }
}
